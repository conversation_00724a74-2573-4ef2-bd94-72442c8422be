{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/playlist-downloader/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nexport default function Home() {\n  const [url, setUrl] = useState('');\n  const [format, setFormat] = useState('mp3');\n  const [loading, setLoading] = useState(false);\n  const [result, setResult] = useState<{\n    status: string;\n    message: string;\n    downloadLink?: string;\n    timestamp?: string;\n    fileCount?: number;\n    format?: string;\n  } | null>(null);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError(null);\n    setResult(null);\n\n    try {\n      const response = await fetch('http://localhost:5000/download', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ url, format }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.message || data.error || 'Ter<PERSON>di kesalahan');\n      }\n\n      setResult(data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Ter<PERSON><PERSON> kesalahan');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreateZip = async () => {\n    setLoading(true);\n    setError(null);\n    setResult(null);\n\n    try {\n      const response = await fetch('http://localhost:5000/create-zip', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.message || data.error || 'Terjadi kesalahan');\n      }\n\n      setResult(data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Terjadi kesalahan');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"max-w-2xl mx-auto\">\n          {/* Header */}\n          <div className=\"text-center mb-8\">\n            <h1 className=\"text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              🎵 YouTube Playlist Downloader\n            </h1>\n            <p className=\"text-lg text-gray-600 dark:text-gray-300\">\n              Download seluruh playlist YouTube dalam format MP3 atau MP4\n            </p>\n          </div>\n\n          {/* Form */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6\">\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              {/* URL Input */}\n              <div>\n                <label htmlFor=\"url\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  URL Playlist YouTube\n                </label>\n                <input\n                  type=\"url\"\n                  id=\"url\"\n                  value={url}\n                  onChange={(e) => setUrl(e.target.value)}\n                  placeholder=\"https://youtube.com/playlist?list=...\"\n                  className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n                  required\n                  disabled={loading}\n                />\n              </div>\n\n              {/* Format Selection */}\n              <div>\n                <label htmlFor=\"format\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Format Download\n                </label>\n                <select\n                  id=\"format\"\n                  value={format}\n                  onChange={(e) => setFormat(e.target.value)}\n                  className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n                  disabled={loading}\n                >\n                  <option value=\"mp3\">MP3 (Audio Only)</option>\n                  <option value=\"mp4\">MP4 (Video)</option>\n                </select>\n              </div>\n\n              {/* Submit Button */}\n              <button\n                type=\"submit\"\n                disabled={loading || !url}\n                className=\"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2\"\n              >\n                {loading ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"></div>\n                    Downloading...\n                  </>\n                ) : (\n                  <>\n                    <span>📥</span>\n                    Download Playlist\n                  </>\n                )}\n              </button>\n            </form>\n\n            {/* Alternative: Create ZIP from existing files */}\n            <div className=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-600\">\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3\">\n                Atau buat ZIP dari file yang sudah didownload sebelumnya:\n              </p>\n              <button\n                onClick={handleCreateZip}\n                disabled={loading}\n                className=\"w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2\"\n              >\n                {loading ? (\n                  <>\n                    <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"></div>\n                    Creating ZIP...\n                  </>\n                ) : (\n                  <>\n                    <span>📦</span>\n                    Buat ZIP dari File yang Ada\n                  </>\n                )}\n              </button>\n            </div>\n          </div>\n\n          {/* Success Result */}\n          {result && (\n            <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6\">\n              <div className=\"flex items-center gap-2 mb-4\">\n                <span className=\"text-green-500\">✅</span>\n                <h3 className=\"text-green-700 dark:text-green-300 font-medium text-lg\">\n                  {result.message}\n                </h3>\n              </div>\n\n              {result.downloadLink && (\n                <div className=\"space-y-3\">\n                  <div className=\"bg-green-100 dark:bg-green-800/30 rounded-lg p-3\">\n                    <p className=\"text-green-700 dark:text-green-300 font-medium\">\n                      📊 Detail Download:\n                    </p>\n                    <ul className=\"text-green-600 dark:text-green-400 text-sm mt-2 space-y-1\">\n                      <li>• Jumlah file: {result.fileCount} file</li>\n                      <li>• Format: {result.format}</li>\n                      {result.timestamp && (\n                        <li>• Waktu: {new Date(result.timestamp).toLocaleString('id-ID')}</li>\n                      )}\n                    </ul>\n                  </div>\n\n                  <a\n                    href={`http://localhost:5000${result.downloadLink}`}\n                    download\n                    className=\"inline-flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200\"\n                  >\n                    <span>📁</span>\n                    Download ZIP File ({result.fileCount} files)\n                  </a>\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* Error Message */}\n          {error && (\n            <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6\">\n              <div className=\"flex items-center gap-2\">\n                <span className=\"text-red-500\">❌</span>\n                <p className=\"text-red-700 dark:text-red-300 font-medium\">Error:</p>\n              </div>\n              <p className=\"text-red-600 dark:text-red-400 mt-1\">{error}</p>\n            </div>\n          )}\n\n\n\n          {/* Instructions */}\n          <div className=\"bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mt-8\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-3\">\n              📋 Cara Penggunaan:\n            </h3>\n            <ol className=\"list-decimal list-inside space-y-2 text-gray-600 dark:text-gray-300\">\n              <li>Masukkan URL playlist YouTube yang ingin didownload</li>\n              <li>Pilih format: MP3 untuk audio saja, MP4 untuk video</li>\n              <li>Klik tombol \"Download Playlist\"</li>\n              <li>Tunggu proses download selesai</li>\n              <li>Download file ZIP yang berisi semua file dari playlist</li>\n            </ol>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAOzB;IACV,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QACT,UAAU;QAEV,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,kCAAkC;gBAC7D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAK;gBAAO;YACrC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI,KAAK,KAAK,IAAI;YAChD;YAEA,UAAU;QACZ,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,WAAW;QACX,SAAS;QACT,UAAU;QAEV,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oCAAoC;gBAC/D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI,KAAK,KAAK,IAAI;YAChD;YAEA,UAAU;QACZ,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwD;;;;;;0CAGtE,8OAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAM1D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDAEtC,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAM,WAAU;0DAAkE;;;;;;0DAGjG,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,OAAO,EAAE,MAAM,CAAC,KAAK;gDACtC,aAAY;gDACZ,WAAU;gDACV,QAAQ;gDACR,UAAU;;;;;;;;;;;;kDAKd,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAS,WAAU;0DAAkE;;;;;;0DAGpG,8OAAC;gDACC,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gDACzC,WAAU;gDACV,UAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,8OAAC;wDAAO,OAAM;kEAAM;;;;;;;;;;;;;;;;;;kDAKxB,8OAAC;wCACC,MAAK;wCACL,UAAU,WAAW,CAAC;wCACtB,WAAU;kDAET,wBACC;;8DACE,8OAAC;oDAAI,WAAU;;;;;;gDAAkE;;yEAInF;;8DACE,8OAAC;8DAAK;;;;;;gDAAS;;;;;;;;;;;;;;0CAQvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAgD;;;;;;kDAG7D,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,wBACC;;8DACE,8OAAC;oDAAI,WAAU;;;;;;gDAAkE;;yEAInF;;8DACE,8OAAC;8DAAK;;;;;;gDAAS;;;;;;;;;;;;;;;;;;;;oBASxB,wBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAiB;;;;;;kDACjC,8OAAC;wCAAG,WAAU;kDACX,OAAO,OAAO;;;;;;;;;;;;4BAIlB,OAAO,YAAY,kBAClB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAiD;;;;;;0DAG9D,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;;4DAAG;4DAAgB,OAAO,SAAS;4DAAC;;;;;;;kEACrC,8OAAC;;4DAAG;4DAAW,OAAO,MAAM;;;;;;;oDAC3B,OAAO,SAAS,kBACf,8OAAC;;4DAAG;4DAAU,IAAI,KAAK,OAAO,SAAS,EAAE,cAAc,CAAC;;;;;;;;;;;;;;;;;;;kDAK9D,8OAAC;wCACC,MAAM,CAAC,qBAAqB,EAAE,OAAO,YAAY,EAAE;wCACnD,QAAQ;wCACR,WAAU;;0DAEV,8OAAC;0DAAK;;;;;;4CAAS;4CACK,OAAO,SAAS;4CAAC;;;;;;;;;;;;;;;;;;;oBAQ9C,uBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAe;;;;;;kDAC/B,8OAAC;wCAAE,WAAU;kDAA6C;;;;;;;;;;;;0CAE5D,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;;;;;;;kCAOxD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAGvE,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/playlist-downloader/frontend/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/playlist-downloader/frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 560, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/playlist-downloader/frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}