const express = require('express');
const cors = require('cors');
const { downloadPlaylist } = require('./downloader');
const path = require('path');

const app = express();
app.use(cors());
app.use(express.json());

// Serve static files dari folder zips
app.use('/zips', express.static(path.join(__dirname, '../zips')));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'YouTube Playlist Downloader API is running' });
});

app.post('/download', async (req, res) => {
  const { url, format } = req.body;

  // Validasi input
  if (!url || !format) {
    return res.status(400).json({
      error: 'Missing required fields',
      message: 'URL dan format harus diisi'
    });
  }

  if (typeof url !== 'string' || typeof format !== 'string') {
    return res.status(400).json({
      error: 'Invalid input type',
      message: 'URL dan format harus berupa string'
    });
  }

  console.log(`Received download request - URL: ${url}, Format: ${format}`);

  try {
    const result = await downloadPlaylist(url, format);
    console.log(`Download completed successfully:`, result);

    res.json({
      status: 'success',
      message: `Download selesai! ${result.fileCount} file berhasil didownload dalam format ${result.format}`,
      downloadLink: result.downloadLink,
      fileCount: result.fileCount,
      format: result.format,
      timestamp: new Date().toISOString()
    });
  } catch (err) {
    console.error('Download error:', err.message);
    res.status(500).json({
      error: 'Download failed',
      message: err.message
    });
  }
});

// Endpoint untuk membuat ZIP dari file yang sudah ada
app.post('/create-zip', async (req, res) => {
  const fs = require('fs');
  const AdmZip = require('adm-zip');

  try {
    const projectRoot = path.resolve(__dirname, '..');
    const downloadDir = path.join(projectRoot, 'downloads');
    const zipDir = path.join(projectRoot, 'zips');
    const timestamp = Date.now();
    const zipFile = path.join(zipDir, `existing-files-${timestamp}.zip`);

    // Pastikan folder zips ada
    if (!fs.existsSync(zipDir)) {
      fs.mkdirSync(zipDir, { recursive: true });
    }

    // Cek file yang ada di downloads
    const files = fs.readdirSync(downloadDir).filter(file =>
      fs.statSync(path.join(downloadDir, file)).isFile()
    );

    if (files.length === 0) {
      return res.status(404).json({
        error: 'No files found',
        message: 'Tidak ada file yang ditemukan di folder downloads'
      });
    }

    console.log(`Creating ZIP from ${files.length} existing files`);

    // Buat zip dari file yang ada
    const zip = new AdmZip();

    files.forEach(file => {
      const filePath = path.join(downloadDir, file);
      zip.addLocalFile(filePath);
    });

    zip.writeZip(zipFile);
    console.log(`ZIP file created: ${zipFile}`);

    // Kosongin folder downloads setelah ZIP dibuat
    files.forEach(file => {
      const filePath = path.join(downloadDir, file);
      fs.unlinkSync(filePath);
    });

    res.json({
      status: 'success',
      message: `ZIP berhasil dibuat dari ${files.length} file yang sudah ada`,
      downloadLink: `/zips/existing-files-${timestamp}.zip`,
      fileCount: files.length,
      format: 'Audio (WebM format)',
      timestamp: new Date().toISOString()
    });
  } catch (err) {
    console.error('Create ZIP error:', err.message);
    res.status(500).json({
      error: 'Create ZIP failed',
      message: err.message
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: 'Terjadi kesalahan pada server'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: 'Endpoint tidak ditemukan'
  });
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Backend running on http://localhost:${PORT}`);
  console.log('Available endpoints:');
  console.log('  GET  /health - Health check');
  console.log('  POST /download - Download playlist');
  console.log('  GET  /zips/* - Serve downloaded files');
});
