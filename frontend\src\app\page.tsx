'use client';

import { useState } from 'react';

export default function Home() {
  const [url, setUrl] = useState('');
  const [format, setFormat] = useState('mp3');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<{
    status: string;
    message: string;
    downloadLink?: string;
    timestamp?: string;
  } | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('http://localhost:5000/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url, format }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || data.error || '<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han');
      }

      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '<PERSON><PERSON><PERSON><PERSON> kesalahan');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateZip = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('http://localhost:5000/create-zip', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || data.error || 'Terjadi kesalahan');
      }

      setResult(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Terjadi kesalahan');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              🎵 YouTube Playlist Downloader
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              Download seluruh playlist YouTube dalam format MP3 atau MP4
            </p>
          </div>

          {/* Form */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* URL Input */}
              <div>
                <label htmlFor="url" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  URL Playlist YouTube
                </label>
                <input
                  type="url"
                  id="url"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  placeholder="https://youtube.com/playlist?list=..."
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  required
                  disabled={loading}
                />
              </div>

              {/* Format Selection */}
              <div>
                <label htmlFor="format" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Format Download
                </label>
                <select
                  id="format"
                  value={format}
                  onChange={(e) => setFormat(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  disabled={loading}
                >
                  <option value="mp3">MP3 (Audio Only)</option>
                  <option value="mp4">MP4 (Video)</option>
                </select>
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                disabled={loading || !url}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    Downloading...
                  </>
                ) : (
                  <>
                    <span>📥</span>
                    Download Playlist
                  </>
                )}
              </button>
            </form>

            {/* Alternative: Create ZIP from existing files */}
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Atau buat ZIP dari file yang sudah didownload sebelumnya:
              </p>
              <button
                onClick={handleCreateZip}
                disabled={loading}
                className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    Creating ZIP...
                  </>
                ) : (
                  <>
                    <span>📦</span>
                    Buat ZIP dari File yang Ada
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Success Result */}
          {result && (
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
              <div className="flex items-center gap-2 mb-4">
                <span className="text-green-500">✅</span>
                <h3 className="text-green-700 dark:text-green-300 font-medium text-lg">
                  {result.message}
                </h3>
              </div>

              {result.downloadLink && (
                <div className="space-y-3">
                  <div className="bg-green-100 dark:bg-green-800/30 rounded-lg p-3">
                    <p className="text-green-700 dark:text-green-300 font-medium">
                      📊 Detail Download:
                    </p>
                    <ul className="text-green-600 dark:text-green-400 text-sm mt-2 space-y-1">
                      <li>• Jumlah file: {result.fileCount} file</li>
                      <li>• Format: {result.format}</li>
                      {result.timestamp && (
                        <li>• Waktu: {new Date(result.timestamp).toLocaleString('id-ID')}</li>
                      )}
                    </ul>
                  </div>

                  <a
                    href={`http://localhost:5000${result.downloadLink}`}
                    download
                    className="inline-flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200"
                  >
                    <span>📁</span>
                    Download ZIP File ({result.fileCount} files)
                  </a>
                </div>
              )}
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
              <div className="flex items-center gap-2">
                <span className="text-red-500">❌</span>
                <p className="text-red-700 dark:text-red-300 font-medium">Error:</p>
              </div>
              <p className="text-red-600 dark:text-red-400 mt-1">{error}</p>
            </div>
          )}



          {/* Instructions */}
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mt-8">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              📋 Cara Penggunaan:
            </h3>
            <ol className="list-decimal list-inside space-y-2 text-gray-600 dark:text-gray-300">
              <li>Masukkan URL playlist YouTube yang ingin didownload</li>
              <li>Pilih format: MP3 untuk audio saja, MP4 untuk video</li>
              <li>Klik tombol "Download Playlist"</li>
              <li>Tunggu proses download selesai</li>
              <li>Download file ZIP yang berisi semua file dari playlist</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}
