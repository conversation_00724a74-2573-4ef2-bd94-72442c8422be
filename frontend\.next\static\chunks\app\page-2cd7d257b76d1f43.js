(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{3792:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>l});var s=r(5155),t=r(2115);function l(){let[e,a]=(0,t.useState)(""),[r,l]=(0,t.useState)("mp3"),[d,n]=(0,t.useState)(!1),[i,o]=(0,t.useState)(null),[c,u]=(0,t.useState)(null),m=async a=>{a.preventDefault(),n(!0),u(null),o(null);try{let a=await fetch("http://localhost:5000/download",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({url:e,format:r})}),s=await a.json();if(!a.ok)throw Error(s.message||s.error||"Terja<PERSON> kesalahan");o(s)}catch(e){u(e instanceof Error?e.message:"Terjadi kesalahan")}finally{n(!1)}},g=async()=>{n(!0),u(null),o(null);try{let e=await fetch("http://localhost:5000/create-zip",{method:"POST",headers:{"Content-Type":"application/json"}}),a=await e.json();if(!e.ok)throw Error(a.message||a.error||"Terjadi kesalahan");o(a)}catch(e){u(e instanceof Error?e.message:"Terjadi kesalahan")}finally{n(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800",children:(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-gray-900 dark:text-white mb-4",children:"\uD83C\uDFB5 YouTube Playlist Downloader"}),(0,s.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-300",children:"Download seluruh playlist YouTube dalam format MP3 atau MP4"})]}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6",children:[(0,s.jsxs)("form",{onSubmit:m,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"url",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"URL Playlist YouTube"}),(0,s.jsx)("input",{type:"url",id:"url",value:e,onChange:e=>a(e.target.value),placeholder:"https://youtube.com/playlist?list=...",className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white",required:!0,disabled:d})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"format",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Format Download"}),(0,s.jsxs)("select",{id:"format",value:r,onChange:e=>l(e.target.value),className:"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white",disabled:d,children:[(0,s.jsx)("option",{value:"mp3",children:"MP3 (Audio Only)"}),(0,s.jsx)("option",{value:"mp4",children:"MP4 (Video)"})]})]}),(0,s.jsx)("button",{type:"submit",disabled:d||!e,className:"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2",children:d?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),"Downloading..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("span",{children:"\uD83D\uDCE5"}),"Download Playlist"]})})]}),(0,s.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-600",children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-3",children:"Atau buat ZIP dari file yang sudah didownload sebelumnya:"}),(0,s.jsx)("button",{onClick:g,disabled:d,className:"w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2",children:d?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),"Creating ZIP..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("span",{children:"\uD83D\uDCE6"}),"Buat ZIP dari File yang Ada"]})})]})]}),i&&(0,s.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,s.jsx)("span",{className:"text-green-500",children:"✅"}),(0,s.jsx)("h3",{className:"text-green-700 dark:text-green-300 font-medium text-lg",children:i.message})]}),i.downloadLink&&(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"bg-green-100 dark:bg-green-800/30 rounded-lg p-3",children:[(0,s.jsx)("p",{className:"text-green-700 dark:text-green-300 font-medium",children:"\uD83D\uDCCA Detail Download:"}),(0,s.jsxs)("ul",{className:"text-green-600 dark:text-green-400 text-sm mt-2 space-y-1",children:[(0,s.jsxs)("li",{children:["• Jumlah file: ",i.fileCount," file"]}),(0,s.jsxs)("li",{children:["• Format: ",i.format]}),i.timestamp&&(0,s.jsxs)("li",{children:["• Waktu: ",new Date(i.timestamp).toLocaleString("id-ID")]})]})]}),(0,s.jsxs)("a",{href:"http://localhost:5000".concat(i.downloadLink),download:!0,className:"inline-flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200",children:[(0,s.jsx)("span",{children:"\uD83D\uDCC1"}),"Download ZIP File (",i.fileCount," files)"]})]})]}),c&&(0,s.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-red-500",children:"❌"}),(0,s.jsx)("p",{className:"text-red-700 dark:text-red-300 font-medium",children:"Error:"})]}),(0,s.jsx)("p",{className:"text-red-600 dark:text-red-400 mt-1",children:c})]}),(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mt-8",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-3",children:"\uD83D\uDCCB Cara Penggunaan:"}),(0,s.jsxs)("ol",{className:"list-decimal list-inside space-y-2 text-gray-600 dark:text-gray-300",children:[(0,s.jsx)("li",{children:"Masukkan URL playlist YouTube yang ingin didownload"}),(0,s.jsx)("li",{children:"Pilih format: MP3 untuk audio saja, MP4 untuk video"}),(0,s.jsx)("li",{children:'Klik tombol "Download Playlist"'}),(0,s.jsx)("li",{children:"Tunggu proses download selesai"}),(0,s.jsx)("li",{children:"Download file ZIP yang berisi semua file dari playlist"})]})]})]})})})}},4311:(e,a,r)=>{Promise.resolve().then(r.bind(r,3792))}},e=>{e.O(0,[441,964,358],()=>e(e.s=4311)),_N_E=e.O()}]);